<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="PhpIncludePathManager">
    <include_path>
      <path value="$PROJECT_DIR$/vendor/composer" />
      <path value="$PROJECT_DIR$/vendor/stripe/stripe-php" />
      <path value="$PROJECT_DIR$/MAILER/vendor/composer" />
      <path value="$PROJECT_DIR$/MAILER/vendor/phpmailer/phpmailer" />
      <path value="$PROJECT_DIR$/staging/MAILER/vendor/composer" />
      <path value="$PROJECT_DIR$/staging/MAILER/vendor/phpmailer/phpmailer" />
      <path value="$PROJECT_DIR$/backup/vendor/composer" />
      <path value="$PROJECT_DIR$/staging/admin/MAILER/vendor/composer" />
      <path value="$PROJECT_DIR$/staging/admin/MAILER/vendor/phpmailer/phpmailer" />
      <path value="$PROJECT_DIR$/cllxware/cllxware/MAILER/vendor/composer" />
      <path value="$PROJECT_DIR$/cllxware/cllxware/MAILER/vendor/phpmailer/phpmailer" />
      <path value="$PROJECT_DIR$/admin/MAILER/vendor/psr/http-client" />
      <path value="$PROJECT_DIR$/admin/MAILER/vendor/markbaker/matrix" />
      <path value="$PROJECT_DIR$/admin/MAILER/vendor/phpoffice/phpspreadsheet" />
      <path value="$PROJECT_DIR$/admin/MAILER/vendor/markbaker/complex" />
      <path value="$PROJECT_DIR$/admin/MAILER/vendor/phpmailer/phpmailer" />
      <path value="$PROJECT_DIR$/admin/MAILER/vendor/composer" />
      <path value="$PROJECT_DIR$/admin/MAILER/vendor/maennchen/zipstream-php" />
      <path value="$PROJECT_DIR$/admin/MAILER/vendor/psr/simple-cache" />
      <path value="$PROJECT_DIR$/admin/MAILER/vendor/psr/http-message" />
      <path value="$PROJECT_DIR$/admin/MAILER/vendor/psr/http-factory" />
      <path value="$PROJECT_DIR$/cllxware/cllxware/backup/vendor/composer" />
      <path value="$PROJECT_DIR$/cllxware/cllxware/backup/vendor/ifsnop/mysqldump-php" />
    </include_path>
  </component>
  <component name="PhpProjectSharedConfiguration" php_language_level="8.1">
    <option name="suggestChangeDefaultLanguageLevel" value="false" />
  </component>
</project>
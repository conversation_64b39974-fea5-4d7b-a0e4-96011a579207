<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Header</title>
<link type="text/css" rel="stylesheet" href="../fpdf.css">
</head>
<body>
<h1>Header</h1>
<code>Header()</code>
<h2>Description</h2>
This method is used to render the page header. It is automatically called by AddPage() and
should not be called directly by the application. The implementation in FPDF is empty, so
you have to subclass it and override the method if you want a specific processing.
<h2>Example</h2>
<div class="doc-source">
<pre><code>class PDF extends FPDF
{
    function Header()
    {
        // Select Arial bold 15
        $this-&gt;SetFont('Arial', 'B', 15);
        // Move to the right
        $this-&gt;Cell(80);
        // Framed title
        $this-&gt;Cell(30, 10, 'Title', 1, 0, 'C');
        // Line break
        $this-&gt;Ln(20);
    }
}</code></pre>
</div>
<h2>See also</h2>
<a href="footer.htm">Footer</a>
<hr style="margin-top:1.5em">
<div style="text-align:center"><a href="index.htm">Index</a></div>
</body>
</html>

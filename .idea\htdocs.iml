<?xml version="1.0" encoding="UTF-8"?>
<module type="JAVA_MODULE" version="4">
  <component name="NewModuleRootManager" inherit-compiler-output="true">
    <exclude-output />
    <content url="file://$MODULE_DIR$">
      <excludeFolder url="file://$MODULE_DIR$/MAILER/vendor/composer" />
      <excludeFolder url="file://$MODULE_DIR$/MAILER/vendor/phpmailer/phpmailer" />
      <excludeFolder url="file://$MODULE_DIR$/admin/MAILER/vendor/composer" />
      <excludeFolder url="file://$MODULE_DIR$/admin/MAILER/vendor/maennchen/zipstream-php" />
      <excludeFolder url="file://$MODULE_DIR$/admin/MAILER/vendor/markbaker/complex" />
      <excludeFolder url="file://$MODULE_DIR$/admin/MAILER/vendor/markbaker/matrix" />
      <excludeFolder url="file://$MODULE_DIR$/admin/MAILER/vendor/phpmailer/phpmailer" />
      <excludeFolder url="file://$MODULE_DIR$/admin/MAILER/vendor/phpoffice/phpspreadsheet" />
      <excludeFolder url="file://$MODULE_DIR$/admin/MAILER/vendor/psr/http-client" />
      <excludeFolder url="file://$MODULE_DIR$/admin/MAILER/vendor/psr/http-factory" />
      <excludeFolder url="file://$MODULE_DIR$/admin/MAILER/vendor/psr/http-message" />
      <excludeFolder url="file://$MODULE_DIR$/admin/MAILER/vendor/psr/simple-cache" />
      <excludeFolder url="file://$MODULE_DIR$/backup/vendor/composer" />
      <excludeFolder url="file://$MODULE_DIR$/cllxware/cllxware/MAILER/vendor/composer" />
      <excludeFolder url="file://$MODULE_DIR$/cllxware/cllxware/MAILER/vendor/phpmailer/phpmailer" />
      <excludeFolder url="file://$MODULE_DIR$/cllxware/cllxware/backup/vendor/composer" />
      <excludeFolder url="file://$MODULE_DIR$/cllxware/cllxware/backup/vendor/ifsnop/mysqldump-php" />
      <excludeFolder url="file://$MODULE_DIR$/staging/MAILER/vendor/composer" />
      <excludeFolder url="file://$MODULE_DIR$/staging/MAILER/vendor/phpmailer/phpmailer" />
      <excludeFolder url="file://$MODULE_DIR$/staging/admin/MAILER/vendor/composer" />
      <excludeFolder url="file://$MODULE_DIR$/staging/admin/MAILER/vendor/phpmailer/phpmailer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/composer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/stripe/stripe-php" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
  </component>
</module>